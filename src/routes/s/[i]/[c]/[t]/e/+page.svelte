<script lang="ts">
	import { subjects } from '$lib/constants';
	import type { Score } from '$lib/types';
  import { onMount } from 'svelte';
	import { enhance } from '$app/forms';
	import { goto } from '$app/navigation';

  export let data: {
    i: string;
    c: string;
    t: string;
    scores: Score[]
  };

  let mounted = false;
  let showAddSubjects = false;
  let added_subjects: string[] = [];
  let isSubmitting = false;

  // Get subject codes already in scores
  $: scored_subjects = data.scores.map(s => s.j);
  // Subjects not yet scored
  $: available_subjects = Object.keys(subjects).filter(code => !scored_subjects.includes(code));

  // Combine existing scores and new subjects for unified rendering
  $: allSubjects = [
    ...data.scores.map(score => ({
      code: score.j,
      name: subjects[score.j as keyof typeof subjects],
      ca1: score[1] || '',
      ca2: score[2] || '',
      isNew: false
    })),
    ...added_subjects.map(code => ({
      code,
      name: subjects[code as keyof typeof subjects],
      ca1: '',
      ca2: '',
      isNew: true
    }))
  ];

  onMount(() => {
    mounted = true;
  });

  function toggleSubject(code: string) {
    if (added_subjects.includes(code)) {
      added_subjects = added_subjects.filter(s => s !== code);
    } else {
      added_subjects = [...added_subjects, code];
    }
  }
</script>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
  <div class="max-w-4xl mx-auto px-6 py-12">
    <!-- Header Section -->
    <div class="mb-12">
      <div class="flex items-center justify-between mb-8">
        <div class="space-y-2">
          <h1 class="text-4xl font-light tracking-tight text-slate-900">
            Edit CA Scores
          </h1>
          <p class="text-lg text-slate-500 font-light">1st & 2nd Continuous Assessment • Term {data.t} • Class {data.c}</p>
        </div>

        <a
          href="/s/{data.i}/{data.c}/{data.t}"
          class="group relative overflow-hidden bg-slate-100 hover:bg-slate-200 text-slate-700 px-8 py-3 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg"
        >
          <span class="relative z-10 font-medium">← Back to View</span>
        </a>
      </div>

      <!-- Add Subjects Button -->
      {#if available_subjects.length > 0}
        <button
          type="button"
          on:click={() => showAddSubjects = !showAddSubjects}
          class="group relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg mb-8"
        >
          <span class="relative z-10 font-medium flex items-center space-x-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <span>Add Subjects</span>
          </span>
        </button>
      {/if}

      <!-- Add Subjects Panel -->
      {#if showAddSubjects && available_subjects.length > 0}
        <div class="bg-white/70 backdrop-blur-sm rounded-3xl p-8 shadow-xl border border-white/20 mb-8">
          <h3 class="text-xl font-light text-slate-900 mb-6">Select subjects to add</h3>
          <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
            {#each available_subjects as code}
              <label class="flex items-center space-x-3 p-4 rounded-2xl hover:bg-slate-50/50 transition-colors cursor-pointer">
                <input
                  type="checkbox"
                  checked={added_subjects.includes(code)}
                  on:change={() => toggleSubject(code)}
                  class="w-5 h-5 text-blue-600 rounded-lg border-2 border-slate-300 focus:ring-blue-500 focus:ring-2"
                />
                <span class="text-slate-700 font-medium">{subjects[code as keyof typeof subjects]}</span>
              </label>
            {/each}
          </div>
          <div class="flex space-x-4">
            <button
              type="button"
              on:click={() => showAddSubjects = false}
              class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-2xl transition-all duration-300 font-medium"
            >
              Done
            </button>
            <button
              type="button"
              on:click={() => { showAddSubjects = false; added_subjects = []; }}
              class="bg-slate-100 hover:bg-slate-200 text-slate-700 px-6 py-3 rounded-2xl transition-all duration-300 font-medium"
            >
              Cancel
            </button>
          </div>
        </div>
      {/if}
    </div>

    <!-- Scores Form -->
    <form
      use:enhance={() => {
			return ({ result }) => {
				if (result.type === 'success') {
					goto(`/s/${data.i}/${data.c}/${data.t}`);
				}
			}
		}}
      method="POST"
      class="space-y-8"
    >
      <input type="hidden" name="studentId" value={data.i} />
      <input type="hidden" name="term" value={data.t} />

      {#if allSubjects.length > 0}
        <div class="bg-white/70 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 overflow-hidden">
          <div class="p-8">
            <h2 class="text-2xl font-light text-slate-900 mb-8">Continuous Assessment Scores</h2>

            <div class="overflow-hidden rounded-2xl border border-slate-200/50">
              <!-- Table Header -->
              <div class="bg-slate-50/50 px-6 py-4 border-b border-slate-200/50">
                <div class="grid grid-cols-12 gap-4 items-center">
                  <div class="col-span-6">
                    <span class="text-sm font-semibold text-slate-700 uppercase tracking-wide">Subject</span>
                  </div>
                  <div class="col-span-3 text-center">
                    <span class="text-sm font-semibold text-slate-700 uppercase tracking-wide">1st CA</span>
                  </div>
                  <div class="col-span-3 text-center">
                    <span class="text-sm font-semibold text-slate-700 uppercase tracking-wide">2nd CA</span>
                  </div>
                </div>
              </div>

              <!-- Table Body -->
              <div class="divide-y divide-slate-200/50">
                {#each allSubjects as subject, i}
                  <div
                    class="group px-6 py-4 transition-all duration-300 {subject.isNew ? 'bg-green-50/30 hover:bg-green-50/50' : 'bg-white hover:bg-blue-50/30'}"
                    class:animate-fade-in={mounted}
                    style="animation-delay: {i * 100}ms"
                  >
                    <div class="grid grid-cols-12 gap-4 items-center">
                      <!-- Subject Name -->
                      <div class="col-span-6 flex items-center space-x-3">
                        <div class="w-3 h-3 rounded-full bg-gradient-to-r {subject.isNew ? 'from-green-400 to-green-600' : 'from-blue-400 to-blue-600'}"></div>
                        <span class="text-lg font-medium text-slate-900">
                          {subject.name}
                        </span>
                        {#if subject.isNew}
                          <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full font-medium">NEW</span>
                        {/if}
                      </div>

                      <!-- 1st CA Input -->
                      <div class="col-span-3 flex items-center justify-center space-x-2">
                        <input
                          id="ca1_{subject.code}"
                          type="number"
                          name={`ca1_${subject.code}`}
                          min="0"
                          max="100"
                          step="1"
                          value={subject.ca1}
                          placeholder="0"
                          class="w-20 px-3 py-2 text-center text-lg font-light bg-white/50 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-300 tabular-nums {subject.isNew ? 'focus:ring-green-500' : 'focus:ring-blue-500'}"
                          disabled={isSubmitting}
                        />
                        <span class="text-slate-500 font-medium">%</span>
                      </div>

                      <!-- 2nd CA Input -->
                      <div class="col-span-3 flex items-center justify-center space-x-2">
                        <input
                          id="ca2_{subject.code}"
                          type="number"
                          name={`ca2_${subject.code}`}
                          min="0"
                          max="100"
                          step="1"
                          value={subject.ca2}
                          placeholder="0"
                          class="w-20 px-3 py-2 text-center text-lg font-light bg-white/50 border border-slate-200 rounded-xl focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-300 tabular-nums {subject.isNew ? 'focus:ring-green-500' : 'focus:ring-blue-500'}"
                          disabled={isSubmitting}
                        />
                        <span class="text-slate-500 font-medium">%</span>
                      </div>
                    </div>
                  </div>
                {/each}
              </div>
            </div>
          </div>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-center pt-8">
          <button
            type="submit"
            disabled={isSubmitting}
            class="group relative overflow-hidden bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-12 py-4 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            <span class="relative z-10 font-semibold text-lg flex items-center space-x-3">
              {#if isSubmitting}
                <svg class="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span>Saving...</span>
              {:else}
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <span>Save CA Scores</span>
              {/if}
            </span>
          </button>
        </div>
      {:else}
        <!-- Empty State -->
        <div class="text-center py-20">
          <div class="w-24 h-24 mx-auto mb-8 rounded-full bg-gradient-to-br from-slate-100 to-slate-200 flex items-center justify-center">
            <svg class="w-10 h-10 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </div>
          <h3 class="text-xl font-light text-slate-900 mb-2">No subjects to score</h3>
          <p class="text-slate-500 mb-8 max-w-md mx-auto">
            Add subjects to start recording scores for this term.
          </p>
          <button
            type="button"
            on:click={() => showAddSubjects = true}
            class="inline-flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <span class="font-medium">Add Subjects</span>
          </button>
        </div>
      {/if}
    </form>
  </div>
</div>

<style>
  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fade-in 0.6s ease-out forwards;
    opacity: 0;
  }
</style>